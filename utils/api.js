// API请求工具类
const BASE_URL = 'https://your-api-domain.com' // 替换为你的API域名

/**
 * 封装微信小程序的网络请求
 * @param {Object} options 请求配置
 * @param {string} options.url 请求地址
 * @param {string} options.method 请求方法 GET/POST/PUT/DELETE
 * @param {Object} options.data 请求数据
 * @param {Object} options.header 请求头
 * @param {boolean} options.loading 是否显示加载提示
 * @param {string} options.loadingText 加载提示文字
 * @returns {Promise} 返回Promise对象
 */
const request = (options = {}) => {
  return new Promise((resolve, reject) => {
    const {
      url,
      method = 'GET',
      data = {},
      header = {},
      loading = true,
      loadingText = '加载中...'
    } = options

    // 显示加载提示
    if (loading) {
      wx.showLoading({
        title: loadingText,
        mask: true
      })
    }

    // 构建完整的请求URL
    const fullUrl = url.startsWith('http') ? url : `${BASE_URL}${url}`

    // 默认请求头
    const defaultHeader = {
      'Content-Type': 'application/json',
      ...header
    }

    // 发起网络请求
    wx.request({
      url: fullUrl,
      method: method.toUpperCase(),
      data,
      header: defaultHeader,
      success: (res) => {
        // 隐藏加载提示
        if (loading) {
          wx.hideLoading()
        }

        // 处理响应
        if (res.statusCode === 200) {
          // 根据你的后端API规范调整这里的逻辑
          if (res.data.code === 0 || res.data.success) {
            resolve(res.data)
          } else {
            // API返回错误
            const errorMsg = res.data.message || res.data.msg || '请求失败'
            wx.showToast({
              title: errorMsg,
              icon: 'none',
              duration: 2000
            })
            reject(new Error(errorMsg))
          }
        } else {
          // HTTP状态码错误
          const errorMsg = `请求失败，状态码：${res.statusCode}`
          wx.showToast({
            title: errorMsg,
            icon: 'none',
            duration: 2000
          })
          reject(new Error(errorMsg))
        }
      },
      fail: (err) => {
        // 隐藏加载提示
        if (loading) {
          wx.hideLoading()
        }

        // 网络错误处理
        let errorMsg = '网络请求失败'
        if (err.errMsg) {
          if (err.errMsg.includes('timeout')) {
            errorMsg = '请求超时，请检查网络连接'
          } else if (err.errMsg.includes('fail')) {
            errorMsg = '网络连接失败，请检查网络设置'
          }
        }

        wx.showToast({
          title: errorMsg,
          icon: 'none',
          duration: 2000
        })
        reject(err)
      }
    })
  })
}

// 便捷方法
const api = {
  // GET请求
  get: (url, data = {}, options = {}) => {
    return request({
      url,
      method: 'GET',
      data,
      ...options
    })
  },

  // POST请求
  post: (url, data = {}, options = {}) => {
    return request({
      url,
      method: 'POST',
      data,
      ...options
    })
  },

  // PUT请求
  put: (url, data = {}, options = {}) => {
    return request({
      url,
      method: 'PUT',
      data,
      ...options
    })
  },

  // DELETE请求
  delete: (url, data = {}, options = {}) => {
    return request({
      url,
      method: 'DELETE',
      data,
      ...options
    })
  }
}

module.exports = {
  request,
  api
}