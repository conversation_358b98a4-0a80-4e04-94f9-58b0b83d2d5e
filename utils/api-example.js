// API使用示例
const { api } = require("./api.js");

// 示例：用户相关API
const userApi = {
  // 获取用户信息
  getUserInfo: (userId) => {
    return api.get(`/user/${userId}`);
  },

  // 更新用户信息
  updateUserInfo: (userData) => {
    return api.post("/user/update", userData);
  },

  // 用户登录
  login: (loginData) => {
    return api.post("/auth/login", loginData, {
      loadingText: "登录中...",
    });
  },
};

// 在页面中的使用方法示例：
/*
// 在页面的js文件中引入
const { api } = require('../../utils/api.js')

Page({
  data: {
    userList: []
  },

  // 获取数据示例
  async loadUserList() {
    try {
      const result = await api.get('/users', { page: 1, limit: 10 })
      this.setData({
        userList: result.data
      })
    } catch (error) {
      console.error('获取用户列表失败:', error)
    }
  },

  // 提交数据示例
  async submitForm() {
    try {
      const formData = {
        name: 'test',
        email: '<EMAIL>'
      }
      const result = await api.post('/user/create', formData)
      wx.showToast({
        title: '提交成功',
        icon: 'success'
      })
    } catch (error) {
      // 错误已在api.js中处理，这里可以做额外处理
      console.error('提交失败:', error)
    }
  }
})
*/

module.exports = {
  userApi,
};
