<!--pages/profile/profile.wxml-->
<view class="container">
  <!-- 主要内容 -->
  <view class="main-content">
    <!-- 头像区域 -->
    <view class="avatar-section">
      <view class="avatar-container" bindtap="onAvatarTap">
        <image class="avatar" src="{{ userInfo.avatar || 'https://via.placeholder.com/120x120/f0f0f0/999999?text=头像' }}" mode="aspectFill"></image>
      </view>
      <text class="upload-text">点击头像上传照片</text>
    </view>

    <!-- 表单区域 -->
    <view class="form-section">
      <!-- 参赛者姓名 -->
      <view class="form-item">
        <text class="label">参赛者姓名</text>
        <input class="input" placeholder="请输入孩子姓名" value="{{ formData.childName }}" bindinput="onChildNameInput" />
      </view>

      <!-- 出生日期 -->
      <view class="form-item">
        <text class="label">出生日期</text>
        <picker mode="date" value="{{ formData.birthDate }}" bindchange="onBirthDateChange">
          <view class="picker-view">
            <text class="picker-text">{{ formData.birthDate || 'XXXX年X月X日' }}</text>
          </view>
        </picker>
      </view>

      <!-- 家长姓名 -->
      <view class="form-item">
        <text class="label">家长姓名</text>
        <input class="input" placeholder="请输入家长姓名" value="{{ formData.parentName }}" bindinput="onParentNameInput" />
      </view>

      <!-- 联系方式 -->
      <view class="form-item">
        <text class="label">联系方式</text>
        <input class="input" placeholder="请输入手机号码" value="{{ formData.phone }}" bindinput="onPhoneInput" type="number" />
      </view>

      <!-- 报名机构 -->
      <view class="form-item">
        <text class="label">报名机构</text>
        <view class="picker-view" bindtap="showOrgSelection">
          <text class="picker-text">{{ selectedOrgName || '请选择报名机构' }}</text>
        </view>
      </view>

      <!-- 报名邀请码 -->
      <view class="form-item">
        <text class="label">报名邀请码</text>
        <input class="input" placeholder="合作门店提供的邀请码" value="{{ formData.inviteCode }}" bindinput="onInviteCodeInput" />
      </view>

      <!-- 参赛达人自我简介 -->
      <view class="form-item textarea-item">
        <text class="label">参赛达人自我简介</text>
        <textarea class="textarea" placeholder="请简单介绍孩子的才艺特长和个人特色，让评委更好地了解小朋友" value="{{ formData.introduction }}" bindinput="onIntroductionInput" maxlength="200"></textarea>
      </view>
    </view>

    <!-- 视频上传区域 -->
    <view class="video-section">
      <view class="video-upload" bindtap="uploadVideo">
        <view class="upload-icon">📷</view>
        <text class="upload-text">点击上传参赛视频</text>
      </view>
    </view>

    <!-- 提示信息 -->
    <view class="tips-section">
      <text class="tips-title">成功获得报名资格后，请提交一段不超过3分钟的儿童才艺表演视频，拍摄要求如下：</text>
      <view class="tips-list">
        <text class="tip-item">1. 使用横屏拍摄，确保画面清晰；</text>
        <text class="tip-item">2. 孩子面部需清晰可见，不得遮挡；</text>
        <text class="tip-item">3. 视频需展示孩子全身；</text>
        <text class="tip-item">4. 禁止使用美颜、滤镜、贴纸等后期加工</text>
      </view>
    </view>

    <!-- 提交按钮 -->
    <view class="submit-section">
      <button class="submit-btn" bindtap="submitForm">提交资料/待报名</button>
    </view>
  </view>

  <!-- 报名机构选择弹窗 -->
  <van-popup
    show="{{ showOrgPopup }}"
    position="bottom"
    round
    bind:close="closeOrgPopup"
  >
    <view class="org-popup">
      <view class="org-header">
        <text class="org-title">选择报名机构</text>
        <text class="org-close" bindtap="closeOrgPopup">×</text>
      </view>
      <view class="org-list">
        <view class="org-item" wx:for="{{ orgList }}" wx:key="id" bindtap="selectOrg" data-org="{{ item }}">
          <view class="org-info">
            <view class="org-name">{{ item.name }}</view>
            <view class="org-province">{{ item.province }}</view>
          </view>
          <view class="org-count">{{ item.count }}人已报名</view>
        </view>
      </view>
    </view>
  </van-popup>
</view>
