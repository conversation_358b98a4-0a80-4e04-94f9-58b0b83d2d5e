/* pages/profile/profile.wxss */
.container {
  min-height: 100vh;
  background: linear-gradient(180deg, #F5C842 0%, #E8E8E8 60%);
}

.main-content {
  padding: 20rpx 40rpx;
}

/* 头像区域 */
.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 40rpx;
  background: white;
  border-radius: 20rpx;
  padding: 40rpx 20rpx 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.avatar-container {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  overflow: hidden;
  background: #f0f0f0;
  margin-bottom: 20rpx;
  position: relative;
}

.avatar {
  width: 100%;
  height: 100%;
}

.upload-text {
  font-size: 28rpx;
  color: #666;
}

/* 表单区域 */
.form-section {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.form-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 40rpx;
  position: relative;
}

.form-item:last-child {
  margin-bottom: 0;
}

.label {
  width: 160rpx;
  font-size: 28rpx;
  color: #333;
  line-height: 60rpx;
  flex-shrink: 0;
}

.input {
  flex: 1;
  height: 60rpx;
  background: #F8F8F8;
  border-radius: 10rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
}

.picker-view {
  flex: 1;
  height: 60rpx;
  background: #F8F8F8;
  border-radius: 10rpx;
  padding: 0 20rpx;
  display: flex;
  align-items: center;
}

.picker-text {
  font-size: 28rpx;
  color: #999;
}

/* 文本域样式 */
.textarea-item {
  flex-direction: column;
  align-items: flex-start;
}

.textarea-item .label {
  margin-bottom: 20rpx;
  line-height: normal;
}

.textarea {
  width: 100%;
  min-height: 200rpx;
  background: #F8F8F8;
  border-radius: 10rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
}

/* 视频上传区域 */
.video-section {
  margin-bottom: 30rpx;
}

.video-upload {
  background: white;
  border: 2rpx dashed #4A90E2;
  border-radius: 20rpx;
  padding: 60rpx 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.upload-icon {
  font-size: 80rpx;
  color: #4A90E2;
  margin-bottom: 20rpx;
}

.video-upload .upload-text {
  font-size: 28rpx;
  color: #4A90E2;
}

/* 提示信息 */
.tips-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.tips-title {
  font-size: 26rpx;
  color: #333;
  line-height: 1.5;
  margin-bottom: 20rpx;
  display: block;
}

.tips-list {
  display: flex;
  flex-direction: column;
}

.tip-item {
  font-size: 24rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 10rpx;
  display: block;
}

.tip-item:last-child {
  margin-bottom: 0;
}

/* 提交按钮 */
.submit-section {
  padding: 0 20rpx 40rpx;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(90deg, #F5C842 0%, #E6B800 100%);
  border-radius: 44rpx;
  color: white;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 20rpx rgba(245, 200, 66, 0.4);
}

.submit-btn:active {
  opacity: 0.8;
  transform: translateY(2rpx);
}

/* 输入框占位符样式 */
.input::-webkit-input-placeholder,
.textarea::-webkit-input-placeholder {
  color: #ccc;
}

/* 机构选择弹窗 */
.org-popup {
  background: white;
  border-radius: 20rpx 20rpx 0 0;
  max-height: 80vh;
}

.org-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 40rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.org-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.org-close {
  font-size: 40rpx;
  color: #999;
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.org-list {
  max-height: 60vh;
  overflow-y: auto;
}

.org-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #f8f8f8;
}

.org-item:last-child {
  border-bottom: none;
}

.org-item:active {
  background: #f8f8f8;
}

.org-info {
  flex: 1;
}

.org-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.org-province {
  font-size: 24rpx;
  color: #999;
}

.org-count {
  font-size: 24rpx;
  color: #F5C842;
}

/* 响应式调整 */
@media screen and (max-width: 375px) {
  .main-content {
    padding: 15rpx 30rpx;
  }

  .label {
    width: 140rpx;
    font-size: 26rpx;
  }

  .input, .picker-text, .textarea {
    font-size: 26rpx;
  }
}
