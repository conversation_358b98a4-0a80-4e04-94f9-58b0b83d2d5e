// pages/profile/profile.js
Page({
  data: {
    userInfo: {
      avatar: ''
    },
    formData: {
      childName: '',
      birthDate: '',
      parentName: '',
      phone: '',
      inviteCode: '',
      introduction: '',
      selectedOrg: ''
    },
    showOrgPopup: false,
    selectedOrgName: '',
    orgList: [
      { id: 'bj', province: '北京市', name: '贝乐熊母婴', count: 1200 },
      { id: 'sh', province: '上海市', name: '贝乐熊母婴', count: 980 },
      { id: 'gz', province: '广东省', name: '贝乐熊母婴', count: 850 },
      { id: 'sz', province: '深圳市', name: '贝乐熊母婴', count: 750 },
      { id: 'zj', province: '浙江省', name: '贝乐熊母婴', count: 500 },
      { id: 'js', province: '江苏省', name: '贝乐熊母婴', count: 420 }
    ]
  },

  onLoad(options) {
    // 页面加载时的逻辑
    this.loadUserData()
  },

  // 加载用户数据
  loadUserData() {
    // 这里可以从本地存储或服务器获取用户数据
    const savedData = wx.getStorageSync('userProfile')
    if (savedData) {
      this.setData({
        formData: savedData
      })

      // 如果有选择的机构，设置机构名称
      if (savedData.selectedOrg) {
        const selectedOrg = this.data.orgList.find(org => org.id === savedData.selectedOrg)
        if (selectedOrg) {
          this.setData({
            selectedOrgName: `${selectedOrg.province} ${selectedOrg.name}`
          })
        }
      }
    }
  },

  // 参赛者姓名输入
  onChildNameInput(e) {
    this.setData({
      'formData.childName': e.detail.value
    })
  },

  // 出生日期选择
  onBirthDateChange(e) {
    this.setData({
      'formData.birthDate': e.detail.value
    })
  },

  // 家长姓名输入
  onParentNameInput(e) {
    this.setData({
      'formData.parentName': e.detail.value
    })
  },

  // 联系方式输入
  onPhoneInput(e) {
    this.setData({
      'formData.phone': e.detail.value
    })
  },

  // 邀请码输入
  onInviteCodeInput(e) {
    this.setData({
      'formData.inviteCode': e.detail.value
    })
  },

  // 自我简介输入
  onIntroductionInput(e) {
    this.setData({
      'formData.introduction': e.detail.value
    })
  },

  // 显示机构选择弹窗
  showOrgSelection() {
    this.setData({
      showOrgPopup: true
    })
  },

  // 关闭机构选择弹窗
  closeOrgPopup() {
    this.setData({
      showOrgPopup: false
    })
  },

  // 选择机构
  selectOrg(e) {
    const org = e.currentTarget.dataset.org
    this.setData({
      'formData.selectedOrg': org.id,
      selectedOrgName: `${org.province} ${org.name}`,
      showOrgPopup: false
    })
  },

  // 点击头像上传
  onAvatarTap() {
    this.uploadAvatar()
  },

  // 上传头像
  uploadAvatar() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFilePath = res.tempFilePaths[0]
        this.setData({
          'userInfo.avatar': tempFilePath
        })
        // 这里可以上传到服务器
        this.uploadImageToServer(tempFilePath)
      }
    })
  },

  // 上传视频
  uploadVideo() {
    wx.chooseVideo({
      sourceType: ['album', 'camera'],
      maxDuration: 180, // 3分钟
      camera: 'back',
      success: (res) => {
        console.log('选择的视频:', res)
        wx.showToast({
          title: '视频上传功能开发中',
          icon: 'none'
        })
        // 这里可以上传视频到服务器
      }
    })
  },

  // 上传图片到服务器
  uploadImageToServer(filePath) {
    // 这里实现图片上传逻辑
    console.log('上传图片:', filePath)
  },

  // 表单验证
  validateForm() {
    const { childName, birthDate, parentName, phone, selectedOrg } = this.data.formData

    if (!childName.trim()) {
      wx.showToast({
        title: '请输入参赛者姓名',
        icon: 'none'
      })
      return false
    }

    if (!birthDate) {
      wx.showToast({
        title: '请选择出生日期',
        icon: 'none'
      })
      return false
    }

    if (!parentName.trim()) {
      wx.showToast({
        title: '请输入家长姓名',
        icon: 'none'
      })
      return false
    }

    if (!phone.trim()) {
      wx.showToast({
        title: '请输入联系方式',
        icon: 'none'
      })
      return false
    }

    // 验证手机号格式
    const phoneReg = /^1[3-9]\d{9}$/
    if (!phoneReg.test(phone)) {
      wx.showToast({
        title: '请输入正确的手机号',
        icon: 'none'
      })
      return false
    }

    if (!selectedOrg) {
      wx.showToast({
        title: '请选择报名机构',
        icon: 'none'
      })
      return false
    }

    return true
  },

  // 提交表单
  submitForm() {
    if (!this.validateForm()) {
      return
    }

    // 保存到本地存储
    wx.setStorageSync('userProfile', this.data.formData)

    wx.showModal({
      title: '提交确认',
      content: '确认提交资料信息吗？',
      success: (res) => {
        if (res.confirm) {
          // 这里可以提交到服务器
          wx.showToast({
            title: '提交成功',
            icon: 'success'
          })
          
          // 延迟返回上一页
          setTimeout(() => {
            wx.navigateBack()
          }, 1500)
        }
      }
    })
  }
})
