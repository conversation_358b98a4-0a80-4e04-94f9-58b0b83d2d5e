// index.js
const { api } = require('../../utils/api.js')

Page({
  data: {
    // 比赛信息
    contestInfo: {
      title: '《少年达人秀》少儿才艺大赛海选阶段',
      participantCount: 25211
    },

    // 参赛里程碑数据
    milestones: [
      { id: 1, text: '解锁报名资格', completed: true },
      { id: 2, text: '解锁大师训练营', completed: false },
      { id: 3, text: '海选赛通过', completed: false },
      { id: 4, text: '大区赛阶段', completed: false },
      { id: 5, text: '总决赛阶段', completed: false }
    ],

    // 弹窗控制
    showOrgPopup: false,
    selectedOrg: '',

    // 报名机构数据
    orgList: [
      { id: 'zj', province: '浙江省', name: '贝乐熊母婴',count: 500 },
      { id: 'hn', province: '湖南省', name: '爱婴宝母婴', count: 400 },
      { id: 'fj', province: '福建省', name: '奶牛市长', count: 300 },
      { id: 'js', province: '江苏省', name: '元安同母婴', count: 450 },
      { id: 'jx', province: '江西省', name: '亲子坊', count: 420 },
      { id: 'gx', province: '广西', name: '爱婴宝', count: 380 }
    ]
  },
  // 功能按钮点击事件
  goToContestDetail() {
    wx.showToast({
      title: '大赛详情功能开发中',
      icon: 'none'
    })
  },

  goToScanCode() {
    wx.scanCode({
      success: (res) => {
        console.log('扫码结果:', res)
        wx.showToast({
          title: '扫码成功',
          icon: 'success'
        })
      },
      fail: (err) => {
        console.error('扫码失败:', err)
        wx.showToast({
          title: '扫码失败',
          icon: 'none'
        })
      }
    })
  },

  goToProfile() {
    wx.showToast({
      title: '个人资料功能开发中',
      icon: 'none'
    })
  },

  // 底部按钮点击事件
  goToRegistration() {
    // 显示报名机构选择弹窗
    this.setData({
      showOrgPopup: true
    })
  },

  goToTraining() {
    wx.showModal({
      title: '训练营缴费',
      content: '确认报名训练营吗？费用1980元',
      confirmText: '确认报名',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          wx.showToast({
            title: '训练营功能开发中',
            icon: 'none'
          })
        }
      }
    })
  },

  // 页面生命周期
  onLoad() {
    console.log('少年达人秀首页加载完成')
  },

  onShow() {
    // 页面显示时可以刷新数据
    this.refreshData()
  },

  // 刷新页面数据
  async refreshData() {
    try {
      // 这里可以调用API获取最新的比赛信息和报名人数
      // const contestData = await api.get('/contest/info')
      // this.setData({
      //   contestInfo: contestData
      // })
      console.log('数据刷新完成')
    } catch (error) {
      console.error('数据刷新失败:', error)
    }
  },

  // 弹窗相关方法
  closeOrgPopup() {
    this.setData({
      showOrgPopup: false
    })
  },

  onOrgChange(event) {
    this.setData({
      selectedOrg: event.detail
    })
    console.log('选择的机构:', event.detail)
  },
})
