<!--index.wxml-->
<scroll-view class="scrollarea" scroll-y type="list">
  <view class="container">
    <!-- 状态栏占位 -->
    <status-bar></status-bar>

    <!-- 头部区域 -->
    <view class="header">
      <view class="title-section">
        <text class="main-title">少年达人秀</text>
        <text class="sub-title">中国首档少年才艺大赛 见证未来之星</text>
      </view>
      <view class="sponsors">
 
      </view>
      <view class="profile-btn">
        <view class="profile-icon">🏠</view>
        <text class="profile-text">报名机构</text>
      </view>
    </view>
    <view class="main">
    

    <!-- 比赛信息卡片 -->
    <view class="contest-box">
        <view class="contest-info">
      <view class="contest-icon">
        <image src='/assets/images/microphone.png' class="micro_icon"></image>
      </view>
      <view class="contest-text">
        <text class="contest-title">《少年达人秀》少儿才艺大赛海选阶段</text>
        <view class="participant-count">
          <text class="count-icon">👤</text>
          <view class="count-text">报名人数 <text class="number">25211</text></view>
        </view>
      </view>
    </view>
    <view class="milestone-section">
      <text class="milestone-title">参赛里程碑</text>

      <view class="milestone-box">
        <view class="milestone-img">
            <image src="/assets/images/banner.png" mode=""/>
        </view>
        <view class="milestone-steps">
        <view class="step-item completed">
          <view class="step-icon">✓</view>
          <text class="step-text">解锁报名资格</text>
        </view>
        <view class="step-item">
          <view class="step-icon">x</view>
          <text class="step-text">解锁大师训练营</text>
        </view>
        <view class="step-item">
          <view class="step-icon">○</view>
          <text class="step-text">海选赛通过</text>
        </view>
        <view class="step-item">
          <view class="step-icon">○</view>
          <text class="step-text">总决赛阶段</text>
        </view>
      </view>
      </view>
    
    </view>
    <view class="function-buttons">
      <view class="function-btn" bindtap="goToContestDetail">
        <view class="btn-icon orange">
            <image class="icon1" src="/assets/images/detail.png" mode=""/>
        </view>
        <text class="btn-text">大赛详情</text>
      </view>
      <view class="function-btn" bindtap="goToScanCode">
        <view class="btn-icon yellow" style="width: 180rpx;height: 180rpx;">
            <image class="icon2" src="/assets/images/scan.png" mode=""/>
        </view>
        <text class="btn-text">扫码通道</text>
      </view>
      <view class="function-btn" bindtap="goToProfile">
        <view class="btn-icon green">
            <image class="icon1" src="/assets/images/we.png" mode=""/>
        </view>
        <text class="btn-text">我的资料</text>
      </view>
    </view>

    </view>
  
    <!-- 参赛里程碑 -->

    <!-- 功能按钮区域 -->

    <!-- 广告区域 -->
    <view class="ad-section">
      <view class="ad-content">
        <view class="ad-text">
          <text class="ad-title">助力每一个孩子的梦想</text>
          <text class="ad-subtitle">营养力为少年达人打CALL</text>
        </view>
        <view class="ad-image">
          <text class="ad-placeholder">产品图片占位</text>
        </view>
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="bottom-buttons">
      <view class="bottom-btn" bindtap="goToRegistration">
        <text class="btn-title">报名缴费通道</text>
        <image class="sigup-banner" src="" mode=""/>
        <text class="btn-price">298元</text>
      </view>
      <view class="bottom-btn" bindtap="goToTraining">
        <text class="btn-title">训练营缴费通道</text>
        <image class="sigup-banner" src="" mode=""/>
        <text class="btn-price">1980元</text>
      </view>
    </view>
</view>
  </view>
</scroll-view>

<!-- 报名机构选择弹窗 -->
<van-popup
  show="{{ showOrgPopup }}"
  position="bottom"
  round
  bind:close="closeOrgPopup"
  custom-style="background: transparent;"
>
  <view class="org-popup">
    <!-- 弹窗头部 -->
    <view class="popup-header">
      <view class="header-bg">
        <view class="title-section">
          <text class="main-title">少年达人秀</text>
          <text class="sub-title">中国首档少年才艺大赛 见证未来之星</text>
        </view>
        <view class="sponsors-mini">
          <view class="sponsor-mini">
            <text class="sponsor-label">主办</text>
            <text class="sponsor-name">SUNNY</text>
            <text class="sponsor-desc">阳光传媒</text>
          </view>
          <view class="sponsor-mini">
            <text class="sponsor-label">特别支持</text>
            <text class="sponsor-name">Caltop</text>
            <text class="sponsor-desc">钙立得</text>
          </view>
          <view class="sponsor-mini">
            <text class="sponsor-label">少年成长</text>
            <text class="sponsor-name">营养专家</text>
          </view>
        </view>
        <view class="home-btn">
          <view class="home-icon">🏠</view>
          <text class="home-text">报名机构</text>
        </view>
      </view>
    </view>

    <!-- 比赛信息 -->
    <view class="contest-info-popup">
      <view class="contest-icon">🏆</view>
      <view class="contest-text">
        <text class="contest-title">《少年达人秀》少儿才艺大赛海选阶段</text>
        <view class="participant-count">
          <text class="count-icon">👤</text>
          <text class="count-text">报名人数25211</text>
        </view>
      </view>
    </view>

    <!-- 报名机构选择 -->
    <view class="org-selection">
      <view class="org-title">报名机构</view>
      <van-radio-group value="{{ selectedOrg }}" bind:change="onOrgChange">
        <view class="org-list">
          <view class="org-item" wx:for="{{ orgList }}" wx:key="id">
            <view class="org-info">
              <view class="province">{{ item.province }}</view>
              <view class="org-detail">
                <text class="org-name">{{ item.name }}</text>
                <text class="org-count">共{{ item.count }}人参赛</text>
              </view>
            </view>
            <van-radio name="{{ item.id }}" checked-color="#ff9500"></van-radio>
          </view>
        </view>
      </van-radio-group>

      <view class="org-note">
        <text>报名机构自动绑定，不可更改</text>
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="popup-buttons">
      <view class="price-btn">
        <text class="btn-title">298元</text>
      </view>
      <view class="price-btn">
        <text class="btn-title">1980元</text>
      </view>
    </view>
  </view>
</van-popup>
