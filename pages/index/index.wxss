/**index.wxss**/
page {
  height: 100vh;
  display: flex;
  flex-direction: column;
 background: linear-gradient(to bottom, #ff0000, #00ff00, #0000ff);
}

.scrollarea {
  flex: 1;
  overflow-y: hidden;
}

.container {
  min-height: 100vh;
}

/* 头部区域 */
.header {
  background: linear-gradient(135deg, #ff9500 0%, #ffb84d 100%);
  padding: 40rpx 30rpx 60rpx;
  position: relative;
  color: white;
}

.title-section {
  text-align: center;
  margin-bottom: 40rpx;
}

.main-title {
  font-size: 48rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 10rpx;
  text-shadow: 2rpx 2rpx 4rpx rgba(0,0,0,0.3);
}

.sub-title {
  font-size: 24rpx;
  opacity: 0.9;
  display: block;
}

.sponsors {
  display: flex;
  justify-content: space-around;
  margin-bottom: 20rpx;
}

.sponsor-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 20rpx;
}

.sponsor-text {
  font-size: 18rpx;
  opacity: 0.8;
  margin-bottom: 4rpx;
}

.sponsor-name {
  font-weight: bold;
  font-size: 22rpx;
  margin-bottom: 2rpx;
}

.sponsor-desc {
  font-size: 18rpx;
  opacity: 0.8;
}

.profile-btn {
  position: absolute;
  right: 30rpx;
  top: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: rgba(255,255,255,0.2);
  border-radius: 20rpx;
  padding: 15rpx;
  min-width: 80rpx;
}

.profile-icon {
  font-size: 32rpx;
  margin-bottom: 5rpx;
}

.profile-text {
  font-size: 20rpx;
}

/* 比赛信息卡片 */
.contest-info {
  background: white;
  margin: 20rpx 30rpx;
  padding: 30rpx;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
  display: flex;
  align-items: center;
}

.contest-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
}

.contest-text {
  flex: 1;
}

.contest-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  display: block;
  margin-bottom: 10rpx;
}

.participant-count {
  display: flex;
  align-items: center;
}

.count-icon {
  font-size: 24rpx;
  margin-right: 8rpx;
  color: #666;
}

.count-text {
  font-size: 24rpx;
  color: #666;
}

/* 参赛里程碑 */
.milestone-section {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  margin: 20rpx 30rpx;
  padding: 40rpx 30rpx;
  border-radius: 20rpx;
  position: relative;
  overflow: hidden;
}

.milestone-title {
  font-size: 32rpx;
  color: #1976d2;
  font-weight: bold;
  display: block;
  margin-bottom: 30rpx;
}

.milestone-steps {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.step-item {
  display: flex;
  align-items: center;
  background: rgba(255,255,255,0.8);
  padding: 20rpx;
  border-radius: 15rpx;
}

.step-icon {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  font-size: 20rpx;
  color: white;
}

.step-item.completed .step-icon {
  background: #4caf50;
}

.step-item:not(.completed) .step-icon {
  background: #90a4ae;
}

.step-text {
  font-size: 26rpx;
  color: #333;
}

.characters {
  position: absolute;
  right: 30rpx;
  bottom: 30rpx;
}

.character {
  font-size: 60rpx;
  position: absolute;
}

.character1 {
  right: 0;
  bottom: 0;
}

.character2 {
  right: 80rpx;
  bottom: 40rpx;
}

/* 功能按钮区域 */
.function-buttons {
  display: flex;
  justify-content: space-around;
  margin: 40rpx 30rpx;
}

.function-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.btn-icon {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  margin-bottom: 15rpx;
  box-shadow: 0 4rpx 15rpx rgba(0,0,0,0.2);
}

.btn-icon.orange {
  background: linear-gradient(135deg, #ff9500 0%, #ffb84d 100%);
}

.btn-icon.yellow {
  background: linear-gradient(135deg, #ffc107 0%, #ffeb3b 100%);
}

.btn-icon.green {
  background: linear-gradient(135deg, #4caf50 0%, #8bc34a 100%);
}

.btn-text {
  font-size: 24rpx;
  color: #333;
}

/* 广告区域 */
.ad-section {
  margin: 40rpx 30rpx;
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.ad-content {
  display: flex;
  align-items: center;
  padding: 30rpx;
}

.ad-text {
  flex: 1;
  padding-right: 20rpx;
}

.ad-title {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
  display: block;
  margin-bottom: 10rpx;
}

.ad-subtitle {
  font-size: 26rpx;
  color: #666;
  display: block;
}

.ad-image {
  width: 200rpx;
  height: 150rpx;
  background: #f0f0f0;
  border-radius: 15rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.ad-placeholder {
  font-size: 24rpx;
  color: #999;
}

/* 底部按钮 */
.bottom-buttons {
  display: flex;
  gap: 20rpx;
  margin: 40rpx 30rpx;
  padding-bottom: 40rpx;
}

.bottom-btn {
  flex: 1;
  background: linear-gradient(135deg, #ffc107 0%, #ffeb3b 100%);
  padding: 40rpx 20rpx;
  border-radius: 20rpx;
  text-align: center;
  box-shadow: 0 4rpx 20rpx rgba(255,193,7,0.3);
}

.btn-title {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
  display: block;
  margin-bottom: 10rpx;
}

.btn-price {
  font-size: 36rpx;
  color: #333;
  font-weight: bold;
  display: block;
}