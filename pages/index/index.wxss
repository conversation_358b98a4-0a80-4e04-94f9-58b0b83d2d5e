/**index.wxss**/
page {
  height: 100vh;
  display: flex;
  flex-direction: column;
 background: linear-gradient(to bottom, #ecb136 20%, #ffffff 40%, #f0f0f0 100%);
}

.scrollarea {
  flex: 1;
  overflow-y: hidden;
}

.container {
  min-height: 100vh;
}

/* 头部区域 */
.header {
  /* background: linear-gradient(135deg, #ff9500 0%, #ffb84d 100%); */
  padding: 40rpx 30rpx 60rpx;
  position: relative;
  color: white;
}

.title-section {
  text-align: center;
  margin-bottom: 40rpx;
}

.main-title {
  font-size: 48rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 10rpx;
  text-shadow: 2rpx 2rpx 4rpx rgba(0,0,0,0.3);
}

.sub-title {
  font-size: 24rpx;
  opacity: 0.9;
  display: block;
}

.sponsors {
  display: flex;
  justify-content: space-around;
  margin-bottom: 20rpx;
}

.sponsor-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 20rpx;
}

.sponsor-text {
  font-size: 18rpx;
  opacity: 0.8;
  margin-bottom: 4rpx;
}

.sponsor-name {
  font-weight: bold;
  font-size: 22rpx;
  margin-bottom: 2rpx;
}

.sponsor-desc {
  font-size: 18rpx;
  opacity: 0.8;
}

.profile-btn {
  position: absolute;
  right: 30rpx;
  bottom: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  /* background: rgba(255,255,255,0.2); */
  border-radius: 20rpx;
  padding: 15rpx;
  min-width: 80rpx;
}

.profile-icon {
  font-size: 32rpx;
  margin-bottom: 5rpx;
}

.profile-text {
  font-size: 20rpx;
  background: #EA882C;
  padding:8rpx 12rpx;
  border-radius:20px;
}
.main{
    padding: 0 32rpx;
    border-radius: 20rpx;
}
.contest-box{
    background: white;
    padding:30rpx;
    border-radius: 20rpx;
    box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}
/* 比赛信息卡片 */
.contest-info {
 
  /* margin: 20rpx 30rpx; */
  /* padding: 30rpx; */


  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.contest-icon {
  width: 65rpx;
  height: 65rpx;
  margin-right: 20rpx;
  border-radius: 50%;
  background: #FBEFC6;
  display: flex;
  align-items: center;
  justify-content: center;
}
.micro_icon{
  width: 40rpx;
  height: 40rpx;
}

.contest-text {
  flex: 1;
}

.contest-title {
  font-size: 26rpx;
  color: #666666;
  /* font-weight: 500; */
  display: block;
  margin-bottom: 10rpx;
}

.participant-count {
  display: flex;
  align-items: center;
}

.count-icon {
  font-size: 24rpx;
  margin-right: 8rpx;
  color: #666;
}

.count-text {
  font-size: 20rpx;
  color: #666;
}
.count-text .number{
    color: #43AFFF;
}

/* 参赛里程碑 */
.milestone-section {
  /* background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%); */
  /* margin: 20rpx 30rpx; */
  /* padding: 40rpx 30rpx; */
  background: #E6F2FA;
  border-radius: 20rpx 20rpx 0 0;
  position: relative;
  padding-top: 20rpx;
    height: 400rpx;
}

.milestone-title {
  font-size: 32rpx;
  color: #43AFFF;
  /* font-weight: bold; */
  display: block;
  padding-left: 30rpx;
  margin-bottom: 30rpx;
}

.milestone-steps {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  position: absolute;
  right: 0;
}
.milestone-box{
    display: flex;

}
.milestone-img{
    width: 405rpx;
    height: 300rpx;
    background: #43AFFF;
    position: absolute;
    bottom:0;
    left:0;
}
.milestone-img image{
    width: 100%;
    height: 100%;
}
.step-item {
  display: flex;
  align-items: center;
  /* background: rgba(255,255,255,0.8); */
  /* padding: 20rpx; */
  border-radius: 15rpx;
}

.step-icon {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  font-size: 20rpx;
  color: white;
}

.step-item.completed .step-icon {
  background: #43AFFF;
}

.step-item:not(.completed) .step-icon {
  background: #90a4ae;
}

.step-text {
  font-size: 24rpx;
  color: #333;
}

.characters {
  position: absolute;
  right: 30rpx;
  bottom: 30rpx;
}

.character {
  font-size: 60rpx;
  position: absolute;
}

.character1 {
  right: 0;
  bottom: 0;
}

.character2 {
  right: 80rpx;
  bottom: 40rpx;
}

/* 功能按钮区域 */
.function-buttons {
  display: flex;
  justify-content: space-around;
  margin: -120rpx 0 0;
  align-items: flex-end;
  position: relative;
  z-index: 999;
}

.function-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.btn-icon {
  width: 124rpx;
  height: 124rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  margin-bottom: 25rpx;
  box-shadow: 0 4rpx 15rpx rgba(0,0,0,0.2);
}
.btn-icon .icon1{
    width: 40rpx;
    height: 40rpx;
}
.btn-icon .icon2{
    width: 80rpx;
    height: 80rpx;
}
.btn-icon.orange {
  background: linear-gradient(135deg, #ff9500 0%, #ffb84d 100%);
}

.btn-icon.yellow {
  background: linear-gradient(135deg, #ffc107 0%, #ffeb3b 100%);
}

.btn-icon.green {
  background: linear-gradient(135deg, #4caf50 0%, #8bc34a 100%);
}

.btn-text {
  font-size: 24rpx;
  color: #666;
}

/* 广告区域 */
.ad-section {
  /* margin: 40rpx 30rpx; */
  margin-top: 30rpx;
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.ad-content {
  display: flex;
  align-items: center;
  padding: 30rpx;
}

.ad-text {
  flex: 1;
  padding-right: 20rpx;
}

.ad-title {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
  display: block;
  margin-bottom: 10rpx;
}

.ad-subtitle {
  font-size: 26rpx;
  color: #666;
  display: block;
}

.ad-image {
  width: 200rpx;
  height: 150rpx;
  background: #f0f0f0;
  border-radius: 15rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.ad-placeholder {
  font-size: 24rpx;
  color: #999;
}

/* 底部按钮 */
.bottom-buttons {
  display: flex;
  gap: 20rpx;
  margin: 40rpx 0;
  padding-bottom: 40rpx;
}

.bottom-btn {
  flex: 1;
  /* background: linear-gradient(135deg, #ffc107 0%, #ffeb3b 100%); */
  /* padding: 40rpx 20rpx; */
  border-radius: 20rpx;
  text-align: center;
  box-shadow: 0 4rpx 20rpx rgba(255,193,7,0.3);
}
.sigup-banner{
    background: #efe4be;
    width: 312rpx;
height: 132rpx;
}

.btn-title {
  font-size: 30rpx;
  height: 75rpx;
  line-height: 75rpx;
  color: #fff;
  /* font-weight: 500; */
  background: #F0B040;
  display: block;
  margin-bottom: 10rpx;
  border-radius: 10rpx 10rpx 0 0;
}

.btn-price {
  font-size: 30rpx;
  color: #040000;
  /* font-weight: bold; */
  display: block;
  text-align: right;
  padding-right: 20rpx;
  padding-bottom: 10rpx;
}

/* 报名机构弹窗样式 */
.org-popup-simple {
  background: white;
  border-radius: 20rpx 20rpx 0 0;
  overflow: hidden;
  max-height: 70vh;
}

/* 机构选择区域 */
.org-selection {
  background: white;
  overflow: hidden;
}

.org-title {
  background: #ffc107;
  color: #333;
  font-size: 28rpx;
  font-weight: bold;
  text-align: center;
  padding: 20rpx;
}

.org-list {
  max-height: 400rpx;
  overflow-y: auto;
}

.org-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 25rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.org-item:last-child {
  border-bottom: none;
}

.org-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.province {
  background: #f5f5f5;
  color: #666;
  font-size: 24rpx;
  padding: 8rpx 15rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
  min-width: 80rpx;
  text-align: center;
}

.org-detail {
  display: flex;
  flex-direction: column;
}

.org-name {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 5rpx;
}

.org-count {
  font-size: 22rpx;
  color: #999;
}

.org-note {
  background: #f5f5f5;
  color: #666;
  font-size: 24rpx;
  text-align: center;
  padding: 20rpx;
}