<!--pages/vant-demo/vant-demo.wxml-->
<view class="container">
  <!-- 通知组件 -->
  <van-notify id="notify" />
  
  <!-- 按钮示例 -->
  <view class="section">
    <view class="section-title">按钮组件</view>
    <van-button type="primary" bind:click="showToast">主要按钮</van-button>
    <van-button type="success" bind:click="showNotify">成功按钮</van-button>
    <van-button type="warning" bind:click="showPopup">警告按钮</van-button>
  </view>

  <!-- 输入框示例 -->
  <view class="section">
    <view class="section-title">输入框组件</view>
    <van-field
      value="{{ value }}"
      placeholder="请输入内容"
      border="{{ false }}"
      bind:change="onChange"
    />
  </view>

  <!-- 下拉菜单示例 -->
  <view class="section">
    <view class="section-title">下拉菜单</view>
    <van-dropdown-menu>
      <van-dropdown-item value="{{ value1 }}" options="{{ option1 }}" bind:change="onDropdownChange" />
    </van-dropdown-menu>
  </view>

  <!-- 步骤条示例 -->
  <view class="section">
    <view class="section-title">步骤条</view>
    <van-steps steps="{{ steps }}" active="{{ active }}" />
  </view>

  <!-- 卡片示例 -->
  <view class="section">
    <view class="section-title">卡片组件</view>
    <van-card
      num="2"
      price="2.00"
      desc="描述信息"
      title="商品标题"
      thumb="https://fastly.jsdelivr.net/npm/@vant/assets/ipad.jpeg"
    >
      <view slot="tags">
        <van-tag plain type="danger">标签</van-tag>
      </view>
    </van-card>
  </view>

  <!-- 单元格示例 -->
  <view class="section">
    <view class="section-title">单元格组件</view>
    <van-cell-group>
      <van-cell title="单元格" value="内容" />
      <van-cell title="单元格" value="内容" label="描述信息" border="{{ false }}" />
    </van-cell-group>
  </view>

  <!-- 弹窗示例 -->
  <van-popup show="{{ show }}" bind:close="onClose">
    <view class="popup-content">
      <view class="popup-title">这是一个弹窗</view>
      <view class="popup-text">弹窗内容</view>
      <van-button type="primary" bind:click="onClose">关闭</van-button>
    </view>
  </van-popup>
</view>