// pages/vant-demo/vant-demo.js
Page({
  data: {
    show: false,
    value: '',
    option1: [
      { text: '全部商品', value: 0 },
      { text: '新款商品', value: 1 },
      { text: '活动商品', value: 2 },
    ],
    value1: 0,
    steps: [
      {
        text: '买家下单',
        desc: '2018-11-11',
      },
      {
        text: '商家接单',
        desc: '2018-11-12',
      },
      {
        text: '买家提货',
        desc: '2018-11-13',
      },
      {
        text: '交易完成',
        desc: '2018-11-14',
      },
    ],
    active: 1,
  },

  // 显示弹窗
  showPopup() {
    this.setData({ show: true });
  },

  // 关闭弹窗
  onClose() {
    this.setData({ show: false });
  },

  // 输入框变化
  onChange(event) {
    this.setData({
      value: event.detail,
    });
  },

  // 下拉菜单变化
  onDropdownChange(event) {
    this.setData({
      value1: event.detail,
    });
  },

  // 显示Toast
  showToast() {
    wx.showToast({
      title: 'Vant组件正常工作！',
      icon: 'success',
      duration: 2000
    });
  },

  // 显示通知
  showNotify() {
    this.selectComponent('#notify').show({
      type: 'success',
      message: '这是一个成功通知',
      duration: 3000
    });
  }
});