/* pages/vant-demo/vant-demo.wxss */
.container {
  padding: 20rpx;
  background-color: #f7f8fa;
  min-height: 100vh;
}

.section {
  margin-bottom: 40rpx;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #323233;
  margin-bottom: 20rpx;
}

.popup-content {
  padding: 60rpx 40rpx;
  text-align: center;
}

.popup-title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  color: #323233;
}

.popup-text {
  font-size: 28rpx;
  color: #646566;
  margin-bottom: 40rpx;
}

/* 按钮间距 */
.van-button {
  margin-right: 20rpx;
  margin-bottom: 20rpx;
}