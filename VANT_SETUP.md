# Vant Weapp 接入指南

## 安装步骤

### 1. 安装依赖
在项目根目录执行：
```bash
npm install @vant/weapp
```

### 2. 构建npm包
在微信开发者工具中：
1. 点击菜单栏的 `工具` -> `构建npm`
2. 等待构建完成

### 3. 使用组件
在页面的 `.json` 文件中引入需要的组件：
```json
{
  "usingComponents": {
    "van-button": "@vant/weapp/button/index",
    "van-field": "@vant/weapp/field/index"
  }
}
```

在 `.wxml` 文件中使用：
```xml
<van-button type="primary">按钮</van-button>
<van-field placeholder="请输入内容" />
```

## 常用组件示例

### 按钮
```xml
<van-button type="primary">主要按钮</van-button>
<van-button type="success">成功按钮</van-button>
<van-button type="warning">警告按钮</van-button>
```

### 输入框
```xml
<van-field
  value="{{ value }}"
  placeholder="请输入内容"
  bind:change="onChange"
/>
```

### 弹窗
```xml
<van-popup show="{{ show }}" bind:close="onClose">
  <view>弹窗内容</view>
</van-popup>
```

### 通知
```xml
<van-notify id="notify" />
```

在JS中调用：
```javascript
this.selectComponent('#notify').show({
  type: 'success',
  message: '成功通知',
  duration: 3000
});
```

## 注意事项

1. **构建npm**: 每次安装新的npm包后都需要重新构建
2. **组件引入**: 只引入需要使用的组件，避免包体积过大
3. **样式覆盖**: 可以通过CSS变量或者!important覆盖默认样式
4. **版本兼容**: 确保基础库版本支持所使用的组件功能

## 更多组件

查看完整的组件列表和文档：
https://vant-contrib.gitee.io/vant-weapp/

## 项目中的示例

- 查看 `pages/vant-demo/` 目录下的完整示例
- 在首页点击"查看Vant组件示例"按钮可以预览效果